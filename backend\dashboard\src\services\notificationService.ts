/**
 * Serviço de notificações para o dashboard administrativo
 */

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  category: 'demand' | 'event' | 'system' | 'general';
  read: boolean;
  actionUrl?: string;
  actionLabel?: string;
  metadata: Record<string, any>;
  createdAt: string;
  readAt?: string;
  expiresAt?: string;
}

export interface NotificationFilters {
  read?: boolean;
  category?: string;
  type?: string;
  limit?: number;
}

class NotificationService {
  private baseURL: string;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';
  }

  /**
   * Fazer requisição HTTP com autenticação
   */
  private async makeRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const token = localStorage.getItem('accessToken');
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(`${this.baseURL}${endpoint}`, config);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  }

  /**
   * Buscar notificações do usuário atual
   */
  async getNotifications(filters?: NotificationFilters): Promise<Notification[]> {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters?.read !== undefined) {
        queryParams.append('read', filters.read.toString());
      }
      if (filters?.category) {
        queryParams.append('category', filters.category);
      }
      if (filters?.type) {
        queryParams.append('type', filters.type);
      }
      if (filters?.limit) {
        queryParams.append('limit', filters.limit.toString());
      }

      const endpoint = queryParams.toString() 
        ? `/notifications?${queryParams.toString()}`
        : '/notifications';

      const response = await this.makeRequest<{ success: boolean; data: Notification[] }>(endpoint);
      return response.data || [];
    } catch (error) {
      console.error('Erro ao buscar notificações:', error);
      return [];
    }
  }

  /**
   * Buscar contagem de notificações não lidas
   */
  async getUnreadCount(): Promise<number> {
    try {
      const response = await this.makeRequest<{ success: boolean; data: { count: number } }>('/notifications/unread-count');
      return response.data?.count || 0;
    } catch (error) {
      console.error('Erro ao buscar contagem de não lidas:', error);
      return 0;
    }
  }

  /**
   * Marcar notificação como lida
   */
  async markAsRead(id: string): Promise<boolean> {
    try {
      await this.makeRequest(`/notifications/${id}/read`, { method: 'PUT' });
      return true;
    } catch (error) {
      console.error('Erro ao marcar como lida:', error);
      return false;
    }
  }

  /**
   * Marcar notificação como não lida
   */
  async markAsUnread(id: string): Promise<boolean> {
    try {
      await this.makeRequest(`/notifications/${id}/unread`, { method: 'PUT' });
      return true;
    } catch (error) {
      console.error('Erro ao marcar como não lida:', error);
      return false;
    }
  }

  /**
   * Marcar todas as notificações como lidas
   */
  async markAllAsRead(): Promise<boolean> {
    try {
      await this.makeRequest('/notifications/mark-all-read', { method: 'PUT' });
      return true;
    } catch (error) {
      console.error('Erro ao marcar todas como lidas:', error);
      return false;
    }
  }

  /**
   * Deletar notificação
   */
  async deleteNotification(id: string): Promise<boolean> {
    try {
      await this.makeRequest(`/notifications/${id}`, { method: 'DELETE' });
      return true;
    } catch (error) {
      console.error('Erro ao deletar notificação:', error);
      return false;
    }
  }

  /**
   * Formatar timestamp relativo
   */
  formatRelativeTime(timestamp: string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Agora';
    if (diffInMinutes < 60) return `${diffInMinutes}m atrás`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h atrás`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d atrás`;
    
    return date.toLocaleDateString('pt-BR');
  }

  /**
   * Obter ícone baseado no tipo da notificação
   */
  getTypeIcon(type: string): string {
    const icons = {
      info: '📢',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    };
    return icons[type as keyof typeof icons] || icons.info;
  }

  /**
   * Obter cor baseada no tipo da notificação
   */
  getTypeColor(type: string): string {
    const colors = {
      info: 'text-blue-600',
      success: 'text-green-600',
      warning: 'text-yellow-600',
      error: 'text-red-600'
    };
    return colors[type as keyof typeof colors] || colors.info;
  }

  /**
   * Obter cor de fundo baseada no tipo da notificação
   */
  getTypeBgColor(type: string): string {
    const colors = {
      info: 'bg-blue-50 border-blue-200',
      success: 'bg-green-50 border-green-200',
      warning: 'bg-yellow-50 border-yellow-200',
      error: 'bg-red-50 border-red-200'
    };
    return colors[type as keyof typeof colors] || colors.info;
  }
}

// Exportar instância única
export const notificationService = new NotificationService();
export default notificationService;
